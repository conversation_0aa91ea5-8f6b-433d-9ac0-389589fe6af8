// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://stvxoaydqjutgqynewva.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InN0dnhvYXlkcWp1dGdxeW5ld3ZhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1NjYwMDksImV4cCI6MjA2NTE0MjAwOX0.HteYOHfynMrlueDm04LN0WgyWzD7tT6ZVltaEx_zdus";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);