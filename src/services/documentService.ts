import { API_ENDPOINTS, makeApiRequest } from '@/lib/utils';

export interface DocumentTemplate {
  id: string;
  name: string;
  nameAr: string;
  nameFr: string;
  description: string;
  descriptionAr: string;
  descriptionFr: string;
  category: string;
  fields: DocumentField[];
  language: 'ar' | 'fr';
}

export interface DocumentField {
  id: string;
  name: string;
  nameAr: string;
  nameFr: string;
  type: 'text' | 'number' | 'date' | 'select' | 'textarea' | 'checkbox';
  required: boolean;
  placeholder?: string;
  placeholderAr?: string;
  placeholderFr?: string;
  options?: string[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
  };
}

export interface DocumentGenerationRequest {
  templateId: string;
  language: 'ar' | 'fr';
  data: Record<string, any>;
  userId: string;
  title: string;
}

export interface DocumentGenerationResponse {
  success: boolean;
  documentId?: string;
  downloadUrl?: string;
  error?: string;
}

// Predefined document templates
export const DOCUMENT_TEMPLATES: DocumentTemplate[] = [
  {
    id: 'contract-rental',
    name: 'Rental Contract',
    nameAr: 'عقد إيجار',
    nameFr: 'Contrat de location',
    description: 'Standard rental agreement template',
    descriptionAr: 'نموذج عقد إيجار قياسي',
    descriptionFr: 'Modèle de contrat de location standard',
    category: 'contracts',
    language: 'ar',
    fields: [
      {
        id: 'landlord_name',
        name: 'Landlord Name',
        nameAr: 'اسم المؤجر',
        nameFr: 'Nom du propriétaire',
        type: 'text',
        required: true,
        placeholderAr: 'أدخل اسم المؤجر',
        placeholderFr: 'Entrez le nom du propriétaire'
      },
      {
        id: 'tenant_name',
        name: 'Tenant Name',
        nameAr: 'اسم المستأجر',
        nameFr: 'Nom du locataire',
        type: 'text',
        required: true,
        placeholderAr: 'أدخل اسم المستأجر',
        placeholderFr: 'Entrez le nom du locataire'
      },
      {
        id: 'property_address',
        name: 'Property Address',
        nameAr: 'عنوان العقار',
        nameFr: 'Adresse de la propriété',
        type: 'textarea',
        required: true,
        placeholderAr: 'أدخل عنوان العقار',
        placeholderFr: 'Entrez l\'adresse de la propriété'
      },
      {
        id: 'monthly_rent',
        name: 'Monthly Rent',
        nameAr: 'الإيجار الشهري',
        nameFr: 'Loyer mensuel',
        type: 'number',
        required: true,
        placeholderAr: 'أدخل قيمة الإيجار الشهري',
        placeholderFr: 'Entrez le montant du loyer mensuel'
      },
      {
        id: 'lease_duration',
        name: 'Lease Duration (months)',
        nameAr: 'مدة الإيجار (بالأشهر)',
        nameFr: 'Durée du bail (mois)',
        type: 'number',
        required: true,
        placeholderAr: 'أدخل مدة الإيجار',
        placeholderFr: 'Entrez la durée du bail'
      },
      {
        id: 'start_date',
        name: 'Start Date',
        nameAr: 'تاريخ البداية',
        nameFr: 'Date de début',
        type: 'date',
        required: true
      }
    ]
  },
  {
    id: 'power-of-attorney',
    name: 'Power of Attorney',
    nameAr: 'توكيل',
    nameFr: 'Procuration',
    description: 'Legal power of attorney document',
    descriptionAr: 'وثيقة توكيل قانونية',
    descriptionFr: 'Document de procuration légale',
    category: 'legal',
    language: 'ar',
    fields: [
      {
        id: 'principal_name',
        name: 'Principal Name',
        nameAr: 'اسم الموكل',
        nameFr: 'Nom du mandant',
        type: 'text',
        required: true,
        placeholderAr: 'أدخل اسم الموكل',
        placeholderFr: 'Entrez le nom du mandant'
      },
      {
        id: 'agent_name',
        name: 'Agent Name',
        nameAr: 'اسم الوكيل',
        nameFr: 'Nom du mandataire',
        type: 'text',
        required: true,
        placeholderAr: 'أدخل اسم الوكيل',
        placeholderFr: 'Entrez le nom du mandataire'
      },
      {
        id: 'powers_granted',
        name: 'Powers Granted',
        nameAr: 'الصلاحيات الممنوحة',
        nameFr: 'Pouvoirs accordés',
        type: 'textarea',
        required: true,
        placeholderAr: 'حدد الصلاحيات الممنوحة للوكيل',
        placeholderFr: 'Spécifiez les pouvoirs accordés au mandataire'
      },
      {
        id: 'duration',
        name: 'Duration',
        nameAr: 'المدة',
        nameFr: 'Durée',
        type: 'select',
        required: true,
        options: ['محدودة المدة', 'غير محدودة المدة', 'Limited Duration', 'Unlimited Duration']
      }
    ]
  }
];

export class DocumentService {
  static async getTemplates(): Promise<DocumentTemplate[]> {
    return DOCUMENT_TEMPLATES;
  }

  static async getTemplate(templateId: string): Promise<DocumentTemplate | null> {
    return DOCUMENT_TEMPLATES.find(template => template.id === templateId) || null;
  }

  static async generateDocument(request: DocumentGenerationRequest): Promise<DocumentGenerationResponse> {
    try {
      // For demo purposes, simulate document generation
      // In production, this would call the actual n8n webhook
      await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000));

      // Generate a mock document ID and download URL
      const documentId = `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const downloadUrl = `#download/${documentId}`;

      return {
        success: true,
        documentId,
        downloadUrl
      };
    } catch (error) {
      console.error('Document generation failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  static async downloadDocument(documentId: string): Promise<Blob | null> {
    try {
      const response = await fetch(`${API_ENDPOINTS.DOCUMENT_GENERATION}/download/${documentId}`);
      if (!response.ok) {
        throw new Error(`Download failed: ${response.status}`);
      }
      return await response.blob();
    } catch (error) {
      console.error('Document download failed:', error);
      return null;
    }
  }
}
