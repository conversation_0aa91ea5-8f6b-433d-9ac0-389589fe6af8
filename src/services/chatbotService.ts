import { API_ENDPOINTS, makeApiRequest } from '@/lib/utils';

export interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  language: 'ar' | 'fr';
  metadata?: {
    confidence?: number;
    sources?: string[];
    category?: string;
  };
}

export interface ChatConversation {
  id: string;
  userId: string;
  title: string;
  messages: ChatMessage[];
  language: 'ar' | 'fr';
  createdAt: Date;
  updatedAt: Date;
  status: 'active' | 'archived';
}

export interface ChatRequest {
  message: string;
  language: 'ar' | 'fr';
  conversationId?: string;
  userId: string;
  context?: {
    previousMessages?: ChatMessage[];
    userProfile?: any;
  };
}

export interface ChatResponse {
  success: boolean;
  message?: string;
  conversationId?: string;
  messageId?: string;
  metadata?: {
    confidence: number;
    sources: string[];
    category: string;
    suggestedActions?: string[];
  };
  error?: string;
}

export class ChatbotService {
  private static conversations: Map<string, ChatConversation> = new Map();

  static async sendMessage(request: ChatRequest): Promise<ChatResponse> {
    try {
      // For demo purposes, simulate API response
      // In production, this would call the actual n8n webhook
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

      // Generate a mock response based on the message
      const mockResponse = this.generateMockResponse(request.message, request.language);

      // Store the conversation locally for quick access
      if (request.conversationId) {
        this.updateLocalConversation(request.conversationId, request, mockResponse);
      }

      return {
        success: true,
        message: mockResponse.message,
        conversationId: request.conversationId || `conv_${Date.now()}`,
        messageId: `msg_${Date.now()}_assistant`,
        metadata: {
          confidence: 0.85,
          sources: ['القانون المغربي', 'مدونة الأسرة'],
          category: 'general',
          suggestedActions: []
        }
      };
    } catch (error) {
      console.error('Chatbot request failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  private static generateMockResponse(message: string, language: 'ar' | 'fr'): any {
    const responses = {
      ar: [
        'شكراً لسؤالك. بناءً على القانون المغربي، يمكنني أن أوضح لك أن...',
        'هذا سؤال مهم في القانون المغربي. وفقاً للمادة المعنية...',
        'في هذه الحالة، ينص القانون المغربي على أن...',
        'بحسب التشريع المغربي الحالي، فإن...',
        'هذا الأمر منظم في القانون المغربي كما يلي...'
      ],
      fr: [
        'Merci pour votre question. Selon le droit marocain, je peux vous expliquer que...',
        'C\'est une question importante en droit marocain. Conformément à l\'article concerné...',
        'Dans ce cas, le droit marocain stipule que...',
        'Selon la législation marocaine actuelle...',
        'Cette question est réglementée par le droit marocain comme suit...'
      ]
    };

    const responseList = responses[language];
    const randomResponse = responseList[Math.floor(Math.random() * responseList.length)];

    return {
      message: randomResponse + ' ' + (language === 'ar' ? 'هل تحتاج لمزيد من التوضيح؟' : 'Avez-vous besoin de plus de clarifications?'),
      confidence: 0.85,
      sources: language === 'ar' ? ['القانون المغربي', 'مدونة الأسرة'] : ['Droit marocain', 'Code de la famille'],
      category: 'general'
    };
  }

  static async getConversation(conversationId: string): Promise<ChatConversation | null> {
    return this.conversations.get(conversationId) || null;
  }

  static async getUserConversations(userId: string): Promise<ChatConversation[]> {
    return Array.from(this.conversations.values())
      .filter(conv => conv.userId === userId)
      .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
  }

  static async createConversation(userId: string, language: 'ar' | 'fr', title?: string): Promise<string> {
    const conversationId = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const conversation: ChatConversation = {
      id: conversationId,
      userId,
      title: title || (language === 'ar' ? 'محادثة جديدة' : 'Nouvelle conversation'),
      messages: [],
      language,
      createdAt: new Date(),
      updatedAt: new Date(),
      status: 'active'
    };

    this.conversations.set(conversationId, conversation);
    return conversationId;
  }

  static async archiveConversation(conversationId: string): Promise<boolean> {
    const conversation = this.conversations.get(conversationId);
    if (conversation) {
      conversation.status = 'archived';
      conversation.updatedAt = new Date();
      return true;
    }
    return false;
  }

  static async deleteConversation(conversationId: string): Promise<boolean> {
    return this.conversations.delete(conversationId);
  }

  private static updateLocalConversation(
    conversationId: string, 
    request: ChatRequest, 
    response: any
  ): void {
    let conversation = this.conversations.get(conversationId);
    
    if (!conversation) {
      conversation = {
        id: conversationId,
        userId: request.userId,
        title: this.generateConversationTitle(request.message, request.language),
        messages: [],
        language: request.language,
        createdAt: new Date(),
        updatedAt: new Date(),
        status: 'active'
      };
    }

    // Add user message
    const userMessage: ChatMessage = {
      id: `msg_${Date.now()}_user`,
      content: request.message,
      role: 'user',
      timestamp: new Date(),
      language: request.language
    };

    // Add assistant response
    const assistantMessage: ChatMessage = {
      id: response.messageId || `msg_${Date.now()}_assistant`,
      content: response.message,
      role: 'assistant',
      timestamp: new Date(),
      language: request.language,
      metadata: {
        confidence: response.confidence,
        sources: response.sources,
        category: response.category
      }
    };

    conversation.messages.push(userMessage, assistantMessage);
    conversation.updatedAt = new Date();
    
    this.conversations.set(conversationId, conversation);
  }

  private static generateConversationTitle(firstMessage: string, language: 'ar' | 'fr'): string {
    // Extract first few words as title
    const words = firstMessage.trim().split(' ').slice(0, 4);
    const title = words.join(' ');
    
    if (title.length > 30) {
      return title.substring(0, 27) + '...';
    }
    
    return title || (language === 'ar' ? 'محادثة جديدة' : 'Nouvelle conversation');
  }

  // Predefined quick questions for different categories
  static getQuickQuestions(language: 'ar' | 'fr'): { category: string; questions: string[] }[] {
    if (language === 'ar') {
      return [
        {
          category: 'قانون الأسرة',
          questions: [
            'ما هي إجراءات الطلاق في المغرب؟',
            'كيف يتم تقسيم الممتلكات بعد الطلاق؟',
            'ما هي حقوق الحضانة للأطفال؟'
          ]
        },
        {
          category: 'قانون العمل',
          questions: [
            'ما هي حقوق العامل في المغرب؟',
            'كيف يتم حساب التعويض عن الفصل؟',
            'ما هي مدة الإشعار المطلوبة للاستقالة؟'
          ]
        },
        {
          category: 'قانون العقارات',
          questions: [
            'ما هي إجراءات شراء عقار في المغرب؟',
            'كيف يتم تسجيل العقار؟',
            'ما هي الضرائب المطلوبة على العقارات؟'
          ]
        }
      ];
    } else {
      return [
        {
          category: 'Droit de la famille',
          questions: [
            'Quelles sont les procédures de divorce au Maroc?',
            'Comment se fait le partage des biens après divorce?',
            'Quels sont les droits de garde des enfants?'
          ]
        },
        {
          category: 'Droit du travail',
          questions: [
            'Quels sont les droits du travailleur au Maroc?',
            'Comment calculer l\'indemnité de licenciement?',
            'Quelle est la durée de préavis requise pour démissionner?'
          ]
        },
        {
          category: 'Droit immobilier',
          questions: [
            'Quelles sont les procédures d\'achat immobilier au Maroc?',
            'Comment enregistrer un bien immobilier?',
            'Quelles sont les taxes requises sur l\'immobilier?'
          ]
        }
      ];
    }
  }
}
