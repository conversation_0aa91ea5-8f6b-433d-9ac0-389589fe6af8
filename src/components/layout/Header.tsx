
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { LanguageSwitcher } from '@/components/LanguageSwitcher';
import { AuthModal } from '@/components/auth/AuthModal';
import { useAuth } from '@/contexts/AuthContext';
import { Scale, User, LogOut, FileText, MessageCircle, LayoutDashboard, Bot } from 'lucide-react';

export const Header = () => {
  const { t } = useTranslation();
  const { user, profile, signOut } = useAuth();
  const navigate = useNavigate();
  const [authModalOpen, setAuthModalOpen] = useState(false);

  const handleSignOut = async () => {
    await signOut();
    navigate('/');
  };

  const navigationItems = [
    { key: 'nav.home', href: '/', icon: Scale },
    { key: 'nav.pricing', href: '/#pricing', icon: null },
  ];

  const userNavigationItems = user ? [
    { key: 'nav.dashboard', href: '/dashboard', icon: LayoutDashboard },
    { key: 'nav.questions', href: '/questions', icon: MessageCircle },
    { key: 'nav.documents', href: '/documents', icon: FileText },
    { key: 'nav.chatbot', href: '/chatbot', icon: Bot },
    ...(profile?.role === 'admin' ? [{ key: 'nav.admin', href: '/admin', icon: Scale }] : []),
  ] : [];

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center space-x-4 space-x-reverse">
            <Link to="/" className="flex items-center space-x-2 space-x-reverse">
              <Scale className="h-8 w-8 text-blue-600" />
              <span className="font-bold text-xl text-gray-900">
                المساعدة القانونية
              </span>
            </Link>
          </div>

          <nav className="hidden md:flex items-center space-x-6 space-x-reverse">
            {navigationItems.map((item) => (
              <Link
                key={item.key}
                to={item.href}
                className="text-sm font-medium transition-colors hover:text-blue-600"
              >
                {t(item.key)}
              </Link>
            ))}
            {userNavigationItems.map((item) => (
              <Link
                key={item.key}
                to={item.href}
                className="text-sm font-medium transition-colors hover:text-blue-600 flex items-center gap-1"
              >
                {item.icon && <item.icon className="h-4 w-4" />}
                {t(item.key)}
              </Link>
            ))}
          </nav>

          <div className="flex items-center space-x-4 space-x-reverse">
            <LanguageSwitcher />
            
            {user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                    <User className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end" forceMount>
                  <div className="flex items-center justify-start gap-2 p-2">
                    <div className="flex flex-col space-y-1 leading-none">
                      <p className="font-medium">{profile?.full_name || user.email}</p>
                      <p className="w-[200px] truncate text-xs text-muted-foreground">
                        {user.email}
                      </p>
                    </div>
                  </div>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link to="/dashboard" className="cursor-pointer">
                      <LayoutDashboard className="mr-2 h-4 w-4" />
                      {t('nav.dashboard')}
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link to="/questions" className="cursor-pointer">
                      <MessageCircle className="mr-2 h-4 w-4" />
                      {t('nav.questions')}
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link to="/documents" className="cursor-pointer">
                      <FileText className="mr-2 h-4 w-4" />
                      {t('nav.documents')}
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link to="/chatbot" className="cursor-pointer">
                      <Bot className="mr-2 h-4 w-4" />
                      {t('nav.chatbot')}
                    </Link>
                  </DropdownMenuItem>
                  {profile?.role === 'admin' && (
                    <DropdownMenuItem asChild>
                      <Link to="/admin" className="cursor-pointer">
                        <Scale className="mr-2 h-4 w-4" />
                        {t('nav.admin')}
                      </Link>
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleSignOut} className="cursor-pointer">
                    <LogOut className="mr-2 h-4 w-4" />
                    {t('nav.logout')}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Button onClick={() => setAuthModalOpen(true)}>
                {t('nav.login')}
              </Button>
            )}
          </div>
        </div>
      </div>

      <AuthModal 
        isOpen={authModalOpen} 
        onClose={() => setAuthModalOpen(false)} 
      />
    </header>
  );
};
