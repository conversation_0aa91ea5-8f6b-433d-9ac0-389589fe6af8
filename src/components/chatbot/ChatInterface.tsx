import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { 
  ChatbotService, 
  ChatMessage, 
  ChatConversation 
} from '@/services/chatbotService';
import { 
  MessageCircle, 
  Send, 
  Bot, 
  User, 
  Loader2, 
  Plus,
  Archive,
  Trash2
} from 'lucide-react';

interface ChatInterfaceProps {
  conversationId?: string;
  onConversationChange?: (conversationId: string) => void;
}

export const ChatInterface: React.FC<ChatInterfaceProps> = ({
  conversationId,
  onConversationChange
}) => {
  const { user } = useAuth();
  const { t, i18n } = useTranslation();
  const { toast } = useToast();
  const [currentConversation, setCurrentConversation] = useState<ChatConversation | null>(null);
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const currentLanguage = i18n.language as 'ar' | 'fr';

  useEffect(() => {
    if (conversationId) {
      loadConversation(conversationId);
    } else if (user) {
      createNewConversation();
    }
  }, [conversationId, user]);

  useEffect(() => {
    scrollToBottom();
  }, [currentConversation?.messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const loadConversation = async (convId: string) => {
    try {
      const conversation = await ChatbotService.getConversation(convId);
      setCurrentConversation(conversation);
    } catch (error) {
      console.error('Failed to load conversation:', error);
      toast({
        title: t('common.error'),
        description: 'فشل في تحميل المحادثة',
        variant: 'destructive'
      });
    }
  };

  const createNewConversation = async () => {
    if (!user) return;

    try {
      const newConversationId = await ChatbotService.createConversation(
        user.id,
        currentLanguage
      );

      const newConversation: ChatConversation = {
        id: newConversationId,
        userId: user.id,
        title: currentLanguage === 'ar' ? 'محادثة جديدة' : 'Nouvelle conversation',
        messages: [],
        language: currentLanguage,
        createdAt: new Date(),
        updatedAt: new Date(),
        status: 'active'
      };

      setCurrentConversation(newConversation);
      onConversationChange?.(newConversationId);
    } catch (error) {
      console.error('Failed to create conversation:', error);
      toast({
        title: currentLanguage === 'ar' ? 'خطأ' : 'Erreur',
        description: currentLanguage === 'ar'
          ? 'فشل في إنشاء محادثة جديدة'
          : 'Échec de la création d\'une nouvelle conversation',
        variant: 'destructive'
      });
    }
  };

  const sendMessage = async () => {
    if (!message.trim() || !user || !currentConversation) return;

    const userMessage = message.trim();
    setMessage('');
    setIsLoading(true);
    setIsTyping(true);

    try {
      const response = await ChatbotService.sendMessage({
        message: userMessage,
        language: currentLanguage,
        conversationId: currentConversation.id,
        userId: user.id,
        context: {
          previousMessages: currentConversation.messages.slice(-5)
        }
      });

      if (response.success && response.message) {
        // Update local conversation with new messages
        const updatedConversation = await ChatbotService.getConversation(currentConversation.id);
        if (updatedConversation) {
          setCurrentConversation(updatedConversation);
        }
      } else {
        throw new Error(response.error || 'Failed to send message');
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      toast({
        title: t('common.error'),
        description: 'فشل في إرسال الرسالة',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
      setIsTyping(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const quickQuestions = ChatbotService.getQuickQuestions(currentLanguage);

  const handleQuickQuestion = (question: string) => {
    setMessage(question);
  };

  if (!user) {
    return (
      <Card className="h-96 flex items-center justify-center">
        <CardContent>
          <p className="text-gray-500">
            {currentLanguage === 'ar' ? 'يجب تسجيل الدخول للوصول للمساعد القانوني' : 'Vous devez vous connecter pour accéder à l\'assistant juridique'}
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto px-4">
      <Card className="h-[600px] flex flex-col">
        <CardHeader className="border-b flex-shrink-0">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Bot className="h-5 w-5 text-blue-600" />
              {currentLanguage === 'ar' ? 'المساعد القانوني' : 'Assistant Juridique'}
            </CardTitle>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={createNewConversation}
              >
                <Plus className="h-4 w-4" />
                {currentLanguage === 'ar' ? 'محادثة جديدة' : 'Nouvelle conversation'}
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="flex-1 flex flex-col p-0">
          <ScrollArea className="flex-1 p-4">
            {currentConversation?.messages.length === 0 ? (
              <div className="space-y-4">
                <div className="text-center py-8">
                  <Bot className="h-16 w-16 text-blue-600 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">
                    {currentLanguage === 'ar' ? 'مرحباً بك في المساعد القانوني' : 'Bienvenue dans l\'assistant juridique'}
                  </h3>
                  <p className="text-gray-600 mb-6">
                    {currentLanguage === 'ar' 
                      ? 'اطرح أي سؤال قانوني وسأساعدك في الحصول على إجابة دقيقة'
                      : 'Posez toute question juridique et je vous aiderai à obtenir une réponse précise'
                    }
                  </p>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">
                    {currentLanguage === 'ar' ? 'أسئلة سريعة:' : 'Questions rapides:'}
                  </h4>
                  {quickQuestions.map((category, categoryIndex) => (
                    <div key={categoryIndex} className="space-y-2">
                      <Badge variant="outline">{category.category}</Badge>
                      <div className="grid gap-2">
                        {category.questions.map((question, questionIndex) => (
                          <Button
                            key={questionIndex}
                            variant="ghost"
                            className="text-left justify-start h-auto p-3 text-sm"
                            onClick={() => handleQuickQuestion(question)}
                          >
                            {question}
                          </Button>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {currentConversation?.messages.map((msg, index) => (
                  <div
                    key={index}
                    className={`flex gap-3 ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div className={`flex gap-3 max-w-[80%] ${msg.role === 'user' ? 'flex-row-reverse' : ''}`}>
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        msg.role === 'user' ? 'bg-blue-600' : 'bg-gray-200'
                      }`}>
                        {msg.role === 'user' ? (
                          <User className="h-4 w-4 text-white" />
                        ) : (
                          <Bot className="h-4 w-4 text-gray-600" />
                        )}
                      </div>
                      <div className={`rounded-lg p-3 ${
                        msg.role === 'user' 
                          ? 'bg-blue-600 text-white' 
                          : 'bg-gray-100 text-gray-900'
                      }`}>
                        <p className="whitespace-pre-wrap">{msg.content}</p>
                        {msg.metadata?.confidence && (
                          <div className="mt-2 text-xs opacity-70">
                            {currentLanguage === 'ar' ? 'الثقة:' : 'Confiance:'} {Math.round(msg.metadata.confidence * 100)}%
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
                
                {isTyping && (
                  <div className="flex gap-3">
                    <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                      <Bot className="h-4 w-4 text-gray-600" />
                    </div>
                    <div className="bg-gray-100 rounded-lg p-3">
                      <div className="flex gap-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                    </div>
                  </div>
                )}
                
                <div ref={messagesEndRef} />
              </div>
            )}
          </ScrollArea>

          <div className="border-t p-4">
            <div className="flex gap-2">
              <Input
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={
                  currentLanguage === 'ar' 
                    ? 'اكتب سؤالك القانوني هنا...' 
                    : 'Tapez votre question juridique ici...'
                }
                disabled={isLoading}
                className="flex-1"
              />
              <Button 
                onClick={sendMessage} 
                disabled={!message.trim() || isLoading}
                size="icon"
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
