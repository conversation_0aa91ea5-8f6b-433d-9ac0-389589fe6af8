import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { LoadingGrid } from '@/components/ui/loading';
import { 
  DocumentService, 
  DocumentTemplate 
} from '@/services/documentService';
import { 
  FileText, 
  Search, 
  Filter,
  Clock,
  Star
} from 'lucide-react';

interface DocumentTemplateSelectorProps {
  onTemplateSelect: (template: DocumentTemplate) => void;
  selectedTemplateId?: string;
}

export const DocumentTemplateSelector: React.FC<DocumentTemplateSelectorProps> = ({
  onTemplateSelect,
  selectedTemplateId
}) => {
  const { i18n } = useTranslation();
  const [templates, setTemplates] = useState<DocumentTemplate[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<DocumentTemplate[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(true);
  const currentLanguage = i18n.language as 'ar' | 'fr';

  useEffect(() => {
    loadTemplates();
  }, []);

  useEffect(() => {
    filterTemplates();
  }, [templates, searchQuery, selectedCategory, currentLanguage]);

  const loadTemplates = async () => {
    try {
      setIsLoading(true);
      // Simulate a small delay to show loading state
      await new Promise(resolve => setTimeout(resolve, 500));
      const templateList = await DocumentService.getTemplates();
      setTemplates(templateList);
      setFilteredTemplates(templateList);
    } catch (error) {
      console.error('Failed to load templates:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filterTemplates = () => {
    let filtered = templates;

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(template => template.category === selectedCategory);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(template => {
        const name = currentLanguage === 'ar' ? template.nameAr : template.nameFr;
        const description = currentLanguage === 'ar' ? template.descriptionAr : template.descriptionFr;
        return name.toLowerCase().includes(query) || 
               description.toLowerCase().includes(query);
      });
    }

    setFilteredTemplates(filtered);
  };

  const getCategories = () => {
    const categories = Array.from(new Set(templates.map(t => t.category)));
    return categories.map(category => ({
      id: category,
      name: getCategoryName(category)
    }));
  };

  const getCategoryName = (category: string) => {
    const categoryNames: Record<string, { ar: string; fr: string }> = {
      contracts: { ar: 'العقود', fr: 'Contrats' },
      legal: { ar: 'الوثائق القانونية', fr: 'Documents légaux' },
      business: { ar: 'الأعمال', fr: 'Affaires' },
      personal: { ar: 'شخصي', fr: 'Personnel' },
      real_estate: { ar: 'العقارات', fr: 'Immobilier' }
    };
    
    return categoryNames[category]?.[currentLanguage] || category;
  };

  const getTemplateDisplayName = (template: DocumentTemplate) => {
    return currentLanguage === 'ar' ? template.nameAr : template.nameFr;
  };

  const getTemplateDescription = (template: DocumentTemplate) => {
    return currentLanguage === 'ar' ? template.descriptionAr : template.descriptionFr;
  };

  if (isLoading) {
    return <LoadingGrid count={6} columns={3} />;
  }

  const categories = getCategories();

  return (
    <div className="space-y-6">
      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder={
              currentLanguage === 'ar' 
                ? 'ابحث عن نموذج...' 
                : 'Rechercher un modèle...'
            }
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex gap-2 flex-wrap">
          <Button
            variant={selectedCategory === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory('all')}
          >
            {currentLanguage === 'ar' ? 'الكل' : 'Tous'}
          </Button>
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedCategory(category.id)}
            >
              {category.name}
            </Button>
          ))}
        </div>
      </div>

      {/* Templates Grid */}
      {filteredTemplates.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {currentLanguage === 'ar' ? 'لا توجد نماذج' : 'Aucun modèle trouvé'}
            </h3>
            <p className="text-gray-600">
              {currentLanguage === 'ar' 
                ? 'جرب تغيير معايير البحث' 
                : 'Essayez de modifier vos critères de recherche'
              }
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6">
          {filteredTemplates.map((template) => (
            <Card 
              key={template.id}
              className={`cursor-pointer transition-all hover:shadow-lg ${
                selectedTemplateId === template.id 
                  ? 'ring-2 ring-blue-500 border-blue-500' 
                  : 'hover:border-blue-300'
              }`}
              onClick={() => onTemplateSelect(template)}
            >
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg">
                      {getTemplateDisplayName(template)}
                    </CardTitle>
                    <CardDescription className="mt-1">
                      {getTemplateDescription(template)}
                    </CardDescription>
                  </div>
                  <div className="flex flex-col items-end gap-2">
                    <Badge variant="secondary">
                      {getCategoryName(template.category)}
                    </Badge>
                    {template.language && (
                      <Badge variant="outline" className="text-xs">
                        {template.language.toUpperCase()}
                      </Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <FileText className="h-4 w-4" />
                    <span>
                      {template.fields.length} {currentLanguage === 'ar' ? 'حقل' : 'champs'}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Clock className="h-4 w-4" />
                    <span>
                      {currentLanguage === 'ar' 
                        ? `${Math.ceil(template.fields.length / 3)} دقائق تقريباً`
                        : `~${Math.ceil(template.fields.length / 3)} minutes`
                      }
                    </span>
                  </div>

                  {/* Preview of required fields */}
                  <div className="pt-2 border-t">
                    <p className="text-xs text-gray-500 mb-2">
                      {currentLanguage === 'ar' ? 'الحقول المطلوبة:' : 'Champs requis:'}
                    </p>
                    <div className="flex flex-wrap gap-1">
                      {template.fields
                        .filter(field => field.required)
                        .slice(0, 3)
                        .map((field, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {currentLanguage === 'ar' ? field.nameAr : field.nameFr}
                          </Badge>
                        ))}
                      {template.fields.filter(field => field.required).length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{template.fields.filter(field => field.required).length - 3}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};
