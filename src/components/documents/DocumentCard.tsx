import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';

export interface DocumentCardProps {
  name: string;
  onDownload: () => void;
}

export const DocumentCard = ({ name, onDownload }: DocumentCardProps) => {
  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex justify-between items-start gap-4">
          <h3 className="text-lg font-semibold text-right flex-grow">{name}</h3>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={onDownload}
          >
            <Download className="h-4 w-4 ml-2" />
            تحميل
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
