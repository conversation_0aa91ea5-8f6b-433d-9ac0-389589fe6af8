
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Check } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

export const PricingSection = () => {
  const { t } = useTranslation();
  const { user, profile } = useAuth();
  const { toast } = useToast();
  const [loading, setLoading] = useState<string | null>(null);

  const handleSubscribe = async (planId: string) => {
    if (!user) {
      toast({
        title: 'مطلوب تسجيل الدخول',
        description: 'يجب تسجيل الدخول أولاً للاشتراك',
        variant: 'destructive',
      });
      return;
    }

    if (planId === 'free') {
      toast({
        title: 'الخطة المجانية',
        description: 'أنت تستخدم الخطة المجانية بالفعل',
      });
      return;
    }

    setLoading(planId);

    try {
      console.log('Creating checkout session for plan:', planId);
      
      const { data, error } = await supabase.functions.invoke('create-checkout', {
        body: { planId },
      });

      if (error) {
        console.error('Checkout error:', error);
        throw error;
      }

      if (data?.url) {
        console.log('Redirecting to Stripe checkout:', data.url);
        // Open Stripe checkout in a new tab
        window.open(data.url, '_blank');
      } else {
        throw new Error('No checkout URL received');
      }

    } catch (error) {
      console.error('Subscription error:', error);
      toast({
        title: 'خطأ في الاشتراك',
        description: error.message || 'حدث خطأ أثناء إنشاء جلسة الدفع',
        variant: 'destructive',
      });
    } finally {
      setLoading(null);
    }
  };

  const plans = [
    {
      id: 'free',
      title: t('pricing.free.title'),
      price: t('pricing.free.price'),
      features: [
        t('pricing.free.features.questions'),
        t('pricing.free.features.documents'),
        t('pricing.free.features.trial'),
      ],
      cta: t('pricing.free.cta'),
      popular: false,
      variant: 'outline' as const,
    },
    {
      id: 'pro_user',
      title: t('pricing.pro.title'),
      price: t('pricing.pro.price'),
      features: [
        t('pricing.pro.features.questions'),
        t('pricing.pro.features.documents'),
        t('pricing.pro.features.support'),
        t('pricing.pro.features.advocates'),
      ],
      cta: t('pricing.pro.cta'),
      popular: true,
      variant: 'default' as const,
    },
    {
      id: 'pro_advocate',
      title: t('pricing.advocate.title'),
      price: t('pricing.advocate.price'),
      features: [
        t('pricing.advocate.features.featured'),
        t('pricing.advocate.features.booking'),
        t('pricing.advocate.features.availability'),
        t('pricing.advocate.features.priority'),
      ],
      cta: t('pricing.advocate.cta'),
      popular: false,
      variant: 'outline' as const,
    },
  ];

  return (
    <section id="pricing" className="py-12 sm:py-20 bg-gray-50">
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            {t('pricing.title')}
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t('pricing.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 max-w-6xl mx-auto">
          {plans.map((plan) => {
            const isCurrentPlan = profile?.subscription_tier === plan.id;
            const isLoading = loading === plan.id;
            
            return (
              <Card key={plan.id} className={`relative ${plan.popular ? 'border-blue-500 shadow-lg scale-105' : ''}`}>
                {plan.popular && (
                  <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-blue-500">
                    الأكثر شعبية
                  </Badge>
                )}
                
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">{plan.title}</CardTitle>
                  <CardDescription className="text-3xl font-bold text-blue-600">
                    {plan.price}
                  </CardDescription>
                </CardHeader>

                <CardContent className="space-y-4">
                  <ul className="space-y-3">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center gap-3">
                        <Check className="h-5 w-5 text-green-500 flex-shrink-0" />
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <Button 
                    className="w-full mt-6" 
                    variant={plan.variant}
                    size="lg"
                    onClick={() => handleSubscribe(plan.id)}
                    disabled={isCurrentPlan || isLoading}
                  >
                    {isLoading ? 'جاري التحميل...' : isCurrentPlan ? 'الخطة الحالية' : plan.cta}
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {profile?.subscription_tier && profile.subscription_tier !== 'free' && (
          <div className="text-center mt-8">
            <p className="text-gray-600 mb-4">
              تريد إدارة اشتراكك؟
            </p>
            <Button 
              variant="outline"
              onClick={async () => {
                try {
                  const { data, error } = await supabase.functions.invoke('customer-portal');
                  if (error) throw error;
                  if (data?.url) {
                    window.open(data.url, '_blank');
                  }
                } catch (error) {
                  toast({
                    title: 'خطأ',
                    description: 'حدث خطأ أثناء فتح لوحة الإدارة',
                    variant: 'destructive',
                  });
                }
              }}
            >
              إدارة الاشتراك
            </Button>
          </div>
        )}
      </div>
    </section>
  );
};
